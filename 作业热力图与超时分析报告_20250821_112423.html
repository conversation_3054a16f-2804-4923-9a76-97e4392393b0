<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作业热力图与超时分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: bold;
        }
        .header .subtitle {
            margin-top: 10px;
            font-size: 16px;
            opacity: 0.9;
        }
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
        }
        .card-body {
            padding: 25px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .chart-container.large {
            height: 500px;
        }
        .chart-container.medium {
            height: 350px;
        }
        .heatmap-row {
            margin-bottom: 20px;
        }
        .building-heatmap {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px;
            background: #fafafa;
        }
        .building-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .calendar-container {
            background: #fafafa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .calendar-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
        }
        .calendar-legend {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
            color: #666;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 5px;
        }
        .overtime-table {
            max-height: 400px;
            overflow-y: auto;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .overtime-badge {
            background-color: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 作业热力图与超时分析报告</h1>
            <div class="subtitle">
                <i class="fas fa-calendar-alt"></i> 分析期间: 2025-08-01 至 2025-08-31 |
                <i class="fas fa-clock"></i> 生成时间: 2025-08-21 11:24:23
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> 统计概览
            </div>
            <div class="card-body">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">285</div>
                        <div class="stat-label">总作业数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">31</div>
                        <div class="stat-label">分析天数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">涉及栋别</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">35</div>
                        <div class="stat-label">超时作业</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">12.28%</div>
                        <div class="stat-label">超时率</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 整体作业热力图 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-fire"></i> 整体作业热力图（ABC栋汇总）
            </div>
            <div class="card-body">
                <div id="overallHeatmap" class="chart-container large"></div>
            </div>
        </div>

        <!-- 各栋别作业热力图 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-building"></i> 各栋别作业热力图
            </div>
            <div class="card-body">
                <div class="row heatmap-row">
                    <div class="col-md-4">
                        <div class="building-heatmap">
                            <div class="building-title">A栋作业热力图</div>
                            <div id="buildingHeatmapA" class="chart-container medium"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="building-heatmap">
                            <div class="building-title">B栋作业热力图</div>
                            <div id="buildingHeatmapB" class="chart-container medium"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="building-heatmap">
                            <div class="building-title">C栋作业热力图</div>
                            <div id="buildingHeatmapC" class="chart-container medium"></div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>热力图说明：</strong>颜色越深表示该时间段作业数量越多。横轴为日期，纵轴为时间段（8:00-18:00）。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 作业日历热力图 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-alt"></i> 作业日历热力图
            </div>
            <div class="card-body">
                <div class="calendar-container">
                    <div class="calendar-header">
                        <i class="fas fa-calendar-check"></i> 作业密度日历视图
                    </div>
                    <div id="calendarChart" class="chart-container large"></div>
                    <div class="calendar-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ebedf0;"></div>
                            <span>无作业</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #c6e48b;"></div>
                            <span>轻度(1-2个)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #7bc96f;"></div>
                            <span>中度(3-5个)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #239a3b;"></div>
                            <span>重度(6-10个)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #196127;"></div>
                            <span>极重(11个以上)</span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-success">
                            <i class="fas fa-lightbulb"></i>
                            <strong>使用提示：</strong>点击日历中的任意日期可查看详细信息。颜色越深表示该日作业数量越多，有助于识别工作负荷分布和优化排程安排。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 栋别分布 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-building"></i> 栋别作业分布
                    </div>
                    <div class="card-body">
                        <div id="buildingChart" class="chart-container"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-clock"></i> 时间段分布
                    </div>
                    <div class="card-body">
                        <div id="hourlyChart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 超时分析 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> 超时分析
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>超时栋别分布</h5>
                        <div id="overtimeChart" class="chart-container"></div>
                    </div>
                    <div class="col-md-6">
                        <h5>超时作业详情</h5>
                        <div class="overtime-table">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>栋别</th>
                                        <th>作业内容</th>
                                        <th>结束时间</th>
                                        <th>超时时长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2025-08-01</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JH027-SC待检品</td>
                                        <td><span class="overtime-badge">18:30</span></td>
                                        <td>1.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-01</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>JT028周转箱</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-01</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JT026-24电池</td>
                                        <td><span class="overtime-badge">21:00</span></td>
                                        <td>3.5小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-04</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-04</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">21:30</span></td>
                                        <td>4.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-04</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">23:30</span></td>
                                        <td>6.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-05</td>
                                        <td><span class="badge bg-primary">A栋</span></td>
                                        <td>UF009完成品</td>
                                        <td><span class="overtime-badge">18:30</span></td>
                                        <td>1.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-05</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-05</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">21:30</span></td>
                                        <td>4.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-06</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>JT028完成品</td>
                                        <td><span class="overtime-badge">18:30</span></td>
                                        <td>1.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-06</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">20:30</span></td>
                                        <td>3.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-07</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JT026-24电池</td>
                                        <td><span class="overtime-badge">19:00</span></td>
                                        <td>1.5小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-11</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JH027-SD待检品</td>
                                        <td><span class="overtime-badge">19:00</span></td>
                                        <td>1.5小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-11</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-11</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">21:30</span></td>
                                        <td>4.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-12</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JH027-SD待检品</td>
                                        <td><span class="overtime-badge">18:30</span></td>
                                        <td>1.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-12</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JH027-SD待检品</td>
                                        <td><span class="overtime-badge">20:30</span></td>
                                        <td>3.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-12</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-16</td>
                                        <td><span class="badge bg-primary">B栋</span></td>
                                        <td>JT026-24电池</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                    <tr>
                                        <td>2025-08-19</td>
                                        <td><span class="badge bg-primary">C栋</span></td>
                                        <td>US001</td>
                                        <td><span class="overtime-badge">19:30</span></td>
                                        <td>2.0小时</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            const overallHeatmapData = {"data": [[0, 0, 3], [0, 1, 0], [0, 2, 2], [0, 3, 1], [0, 4, 0], [0, 5, 0], [0, 6, 2], [0, 7, 0], [0, 8, 1], [0, 9, 1], [0, 10, 1], [1, 0, 3], [1, 1, 0], [1, 2, 0], [1, 3, 2], [1, 4, 0], [1, 5, 0], [1, 6, 1], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 1], [2, 1, 0], [2, 2, 1], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 4], [3, 1, 0], [3, 2, 2], [3, 3, 1], [3, 4, 0], [3, 5, 2], [3, 6, 0], [3, 7, 1], [3, 8, 0], [3, 9, 1], [3, 10, 0], [4, 0, 4], [4, 1, 0], [4, 2, 2], [4, 3, 2], [4, 4, 0], [4, 5, 3], [4, 6, 0], [4, 7, 2], [4, 8, 1], [4, 9, 1], [4, 10, 0], [5, 0, 4], [5, 1, 0], [5, 2, 2], [5, 3, 1], [5, 4, 0], [5, 5, 2], [5, 6, 0], [5, 7, 2], [5, 8, 0], [5, 9, 0], [5, 10, 1], [6, 0, 4], [6, 1, 0], [6, 2, 3], [6, 3, 0], [6, 4, 0], [6, 5, 2], [6, 6, 1], [6, 7, 1], [6, 8, 1], [6, 9, 0], [6, 10, 0], [7, 0, 4], [7, 1, 0], [7, 2, 3], [7, 3, 0], [7, 4, 0], [7, 5, 3], [7, 6, 0], [7, 7, 1], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 2], [8, 1, 0], [8, 2, 1], [8, 3, 1], [8, 4, 0], [8, 5, 1], [8, 6, 1], [8, 7, 1], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 1], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 4], [10, 1, 0], [10, 2, 3], [10, 3, 0], [10, 4, 0], [10, 5, 2], [10, 6, 0], [10, 7, 2], [10, 8, 0], [10, 9, 2], [10, 10, 0], [11, 0, 4], [11, 1, 0], [11, 2, 3], [11, 3, 1], [11, 4, 0], [11, 5, 2], [11, 6, 2], [11, 7, 1], [11, 8, 2], [11, 9, 1], [11, 10, 1], [12, 0, 3], [12, 1, 0], [12, 2, 2], [12, 3, 1], [12, 4, 0], [12, 5, 0], [12, 6, 1], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 4], [13, 1, 0], [13, 2, 3], [13, 3, 1], [13, 4, 0], [13, 5, 0], [13, 6, 1], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 2], [14, 1, 0], [14, 2, 1], [14, 3, 1], [14, 4, 0], [14, 5, 1], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 3], [15, 1, 0], [15, 2, 0], [15, 3, 2], [15, 4, 0], [15, 5, 0], [15, 6, 1], [15, 7, 0], [15, 8, 0], [15, 9, 1], [15, 10, 0], [16, 0, 1], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 4], [17, 1, 0], [17, 2, 2], [17, 3, 1], [17, 4, 0], [17, 5, 2], [17, 6, 0], [17, 7, 1], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 4], [18, 1, 1], [18, 2, 2], [18, 3, 1], [18, 4, 0], [18, 5, 2], [18, 6, 0], [18, 7, 2], [18, 8, 0], [18, 9, 2], [18, 10, 0], [19, 0, 3], [19, 1, 0], [19, 2, 1], [19, 3, 2], [19, 4, 0], [19, 5, 0], [19, 6, 1], [19, 7, 1], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 4], [20, 1, 0], [20, 2, 2], [20, 3, 2], [20, 4, 0], [20, 5, 0], [20, 6, 1], [20, 7, 0], [20, 8, 1], [20, 9, 0], [20, 10, 0], [21, 0, 4], [21, 1, 0], [21, 2, 1], [21, 3, 2], [21, 4, 0], [21, 5, 1], [21, 6, 2], [21, 7, 1], [21, 8, 0], [21, 9, 1], [21, 10, 0], [22, 0, 3], [22, 1, 0], [22, 2, 1], [22, 3, 2], [22, 4, 0], [22, 5, 1], [22, 6, 2], [22, 7, 0], [22, 8, 0], [22, 9, 1], [22, 10, 0], [23, 0, 1], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 3], [24, 1, 0], [24, 2, 2], [24, 3, 1], [24, 4, 0], [24, 5, 2], [24, 6, 0], [24, 7, 2], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 4], [25, 1, 0], [25, 2, 3], [25, 3, 1], [25, 4, 0], [25, 5, 1], [25, 6, 1], [25, 7, 1], [25, 8, 0], [25, 9, 1], [25, 10, 0], [26, 0, 3], [26, 1, 0], [26, 2, 2], [26, 3, 1], [26, 4, 0], [26, 5, 2], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 4], [27, 1, 0], [27, 2, 2], [27, 3, 1], [27, 4, 0], [27, 5, 0], [27, 6, 1], [27, 7, 0], [27, 8, 0], [27, 9, 1], [27, 10, 0], [28, 0, 3], [28, 1, 0], [28, 2, 1], [28, 3, 1], [28, 4, 0], [28, 5, 0], [28, 6, 1], [28, 7, 1], [28, 8, 1], [28, 9, 0], [28, 10, 0], [29, 0, 3], [29, 1, 0], [29, 2, 1], [29, 3, 2], [29, 4, 0], [29, 5, 0], [29, 6, 1], [29, 7, 1], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 2], [30, 1, 0], [30, 2, 1], [30, 3, 1], [30, 4, 0], [30, 5, 1], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]], "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"], "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"], "max_value": 4};
            const buildingHeatmapsData = {"A": {"data": [[0, 0, 0], [0, 1, 0], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 0], [0, 7, 0], [0, 8, 0], [0, 9, 0], [0, 10, 0], [1, 0, 0], [1, 1, 0], [1, 2, 0], [1, 3, 0], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 1], [3, 1, 0], [3, 2, 0], [3, 3, 0], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 0], [3, 8, 0], [3, 9, 0], [3, 10, 0], [4, 0, 1], [4, 1, 0], [4, 2, 1], [4, 3, 0], [4, 4, 0], [4, 5, 1], [4, 6, 0], [4, 7, 0], [4, 8, 1], [4, 9, 0], [4, 10, 0], [5, 0, 1], [5, 1, 0], [5, 2, 0], [5, 3, 0], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 0], [5, 8, 0], [5, 9, 0], [5, 10, 0], [6, 0, 1], [6, 1, 0], [6, 2, 0], [6, 3, 0], [6, 4, 0], [6, 5, 0], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 0], [7, 0, 1], [7, 1, 0], [7, 2, 0], [7, 3, 0], [7, 4, 0], [7, 5, 0], [7, 6, 0], [7, 7, 0], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 0], [8, 1, 0], [8, 2, 0], [8, 3, 0], [8, 4, 0], [8, 5, 0], [8, 6, 0], [8, 7, 0], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 0], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 1], [10, 1, 0], [10, 2, 0], [10, 3, 0], [10, 4, 0], [10, 5, 0], [10, 6, 0], [10, 7, 0], [10, 8, 0], [10, 9, 0], [10, 10, 0], [11, 0, 1], [11, 1, 0], [11, 2, 1], [11, 3, 0], [11, 4, 0], [11, 5, 0], [11, 6, 1], [11, 7, 0], [11, 8, 1], [11, 9, 0], [11, 10, 0], [12, 0, 0], [12, 1, 0], [12, 2, 0], [12, 3, 0], [12, 4, 0], [12, 5, 0], [12, 6, 0], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 1], [13, 1, 0], [13, 2, 1], [13, 3, 0], [13, 4, 0], [13, 5, 0], [13, 6, 0], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 0], [14, 1, 0], [14, 2, 0], [14, 3, 0], [14, 4, 0], [14, 5, 0], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 0], [15, 1, 0], [15, 2, 0], [15, 3, 0], [15, 4, 0], [15, 5, 0], [15, 6, 0], [15, 7, 0], [15, 8, 0], [15, 9, 0], [15, 10, 0], [16, 0, 1], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 1], [17, 1, 0], [17, 2, 0], [17, 3, 0], [17, 4, 0], [17, 5, 0], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 1], [18, 1, 1], [18, 2, 0], [18, 3, 0], [18, 4, 0], [18, 5, 0], [18, 6, 0], [18, 7, 0], [18, 8, 0], [18, 9, 0], [18, 10, 0], [19, 0, 0], [19, 1, 0], [19, 2, 0], [19, 3, 0], [19, 4, 0], [19, 5, 0], [19, 6, 0], [19, 7, 0], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 1], [20, 1, 0], [20, 2, 0], [20, 3, 1], [20, 4, 0], [20, 5, 0], [20, 6, 0], [20, 7, 0], [20, 8, 0], [20, 9, 0], [20, 10, 0], [21, 0, 1], [21, 1, 0], [21, 2, 0], [21, 3, 0], [21, 4, 0], [21, 5, 0], [21, 6, 0], [21, 7, 0], [21, 8, 0], [21, 9, 0], [21, 10, 0], [22, 0, 0], [22, 1, 0], [22, 2, 0], [22, 3, 0], [22, 4, 0], [22, 5, 0], [22, 6, 0], [22, 7, 0], [22, 8, 0], [22, 9, 0], [22, 10, 0], [23, 0, 1], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 0], [24, 1, 0], [24, 2, 0], [24, 3, 0], [24, 4, 0], [24, 5, 0], [24, 6, 0], [24, 7, 0], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 1], [25, 1, 0], [25, 2, 1], [25, 3, 0], [25, 4, 0], [25, 5, 0], [25, 6, 1], [25, 7, 0], [25, 8, 0], [25, 9, 0], [25, 10, 0], [26, 0, 0], [26, 1, 0], [26, 2, 0], [26, 3, 0], [26, 4, 0], [26, 5, 0], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 1], [27, 1, 0], [27, 2, 1], [27, 3, 0], [27, 4, 0], [27, 5, 0], [27, 6, 0], [27, 7, 0], [27, 8, 0], [27, 9, 0], [27, 10, 0], [28, 0, 0], [28, 1, 0], [28, 2, 0], [28, 3, 0], [28, 4, 0], [28, 5, 0], [28, 6, 0], [28, 7, 0], [28, 8, 0], [28, 9, 0], [28, 10, 0], [29, 0, 0], [29, 1, 0], [29, 2, 0], [29, 3, 0], [29, 4, 0], [29, 5, 0], [29, 6, 0], [29, 7, 0], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 0], [30, 1, 0], [30, 2, 0], [30, 3, 0], [30, 4, 0], [30, 5, 0], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]], "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"], "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"], "max_value": 1}, "B": {"data": [[0, 0, 2], [0, 1, 0], [0, 2, 1], [0, 3, 1], [0, 4, 0], [0, 5, 0], [0, 6, 1], [0, 7, 0], [0, 8, 1], [0, 9, 0], [0, 10, 1], [1, 0, 2], [1, 1, 0], [1, 2, 0], [1, 3, 1], [1, 4, 0], [1, 5, 0], [1, 6, 1], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 1], [2, 1, 0], [2, 2, 1], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 2], [3, 1, 0], [3, 2, 2], [3, 3, 0], [3, 4, 0], [3, 5, 2], [3, 6, 0], [3, 7, 0], [3, 8, 0], [3, 9, 0], [3, 10, 0], [4, 0, 2], [4, 1, 0], [4, 2, 1], [4, 3, 1], [4, 4, 0], [4, 5, 2], [4, 6, 0], [4, 7, 1], [4, 8, 0], [4, 9, 0], [4, 10, 0], [5, 0, 2], [5, 1, 0], [5, 2, 2], [5, 3, 0], [5, 4, 0], [5, 5, 2], [5, 6, 0], [5, 7, 1], [5, 8, 0], [5, 9, 0], [5, 10, 0], [6, 0, 2], [6, 1, 0], [6, 2, 2], [6, 3, 0], [6, 4, 0], [6, 5, 1], [6, 6, 1], [6, 7, 1], [6, 8, 1], [6, 9, 0], [6, 10, 0], [7, 0, 2], [7, 1, 0], [7, 2, 2], [7, 3, 0], [7, 4, 0], [7, 5, 2], [7, 6, 0], [7, 7, 1], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 2], [8, 1, 0], [8, 2, 1], [8, 3, 1], [8, 4, 0], [8, 5, 1], [8, 6, 1], [8, 7, 1], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 1], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 2], [10, 1, 0], [10, 2, 2], [10, 3, 0], [10, 4, 0], [10, 5, 1], [10, 6, 0], [10, 7, 1], [10, 8, 0], [10, 9, 1], [10, 10, 0], [11, 0, 2], [11, 1, 0], [11, 2, 1], [11, 3, 1], [11, 4, 0], [11, 5, 1], [11, 6, 1], [11, 7, 0], [11, 8, 1], [11, 9, 0], [11, 10, 1], [12, 0, 2], [12, 1, 0], [12, 2, 1], [12, 3, 1], [12, 4, 0], [12, 5, 0], [12, 6, 1], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 2], [13, 1, 0], [13, 2, 1], [13, 3, 1], [13, 4, 0], [13, 5, 0], [13, 6, 1], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 1], [14, 1, 0], [14, 2, 1], [14, 3, 0], [14, 4, 0], [14, 5, 1], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 2], [15, 1, 0], [15, 2, 0], [15, 3, 1], [15, 4, 0], [15, 5, 0], [15, 6, 1], [15, 7, 0], [15, 8, 0], [15, 9, 1], [15, 10, 0], [16, 0, 0], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 2], [17, 1, 0], [17, 2, 2], [17, 3, 0], [17, 4, 0], [17, 5, 2], [17, 6, 0], [17, 7, 1], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 2], [18, 1, 0], [18, 2, 2], [18, 3, 0], [18, 4, 0], [18, 5, 2], [18, 6, 0], [18, 7, 1], [18, 8, 0], [18, 9, 1], [18, 10, 0], [19, 0, 2], [19, 1, 0], [19, 2, 1], [19, 3, 1], [19, 4, 0], [19, 5, 0], [19, 6, 1], [19, 7, 0], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 2], [20, 1, 0], [20, 2, 1], [20, 3, 1], [20, 4, 0], [20, 5, 0], [20, 6, 1], [20, 7, 0], [20, 8, 1], [20, 9, 0], [20, 10, 0], [21, 0, 2], [21, 1, 0], [21, 2, 1], [21, 3, 1], [21, 4, 0], [21, 5, 1], [21, 6, 1], [21, 7, 1], [21, 8, 0], [21, 9, 1], [21, 10, 0], [22, 0, 2], [22, 1, 0], [22, 2, 1], [22, 3, 1], [22, 4, 0], [22, 5, 1], [22, 6, 1], [22, 7, 0], [22, 8, 0], [22, 9, 1], [22, 10, 0], [23, 0, 0], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 2], [24, 1, 0], [24, 2, 2], [24, 3, 0], [24, 4, 0], [24, 5, 2], [24, 6, 0], [24, 7, 1], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 2], [25, 1, 0], [25, 2, 2], [25, 3, 0], [25, 4, 0], [25, 5, 1], [25, 6, 0], [25, 7, 0], [25, 8, 0], [25, 9, 0], [25, 10, 0], [26, 0, 2], [26, 1, 0], [26, 2, 1], [26, 3, 1], [26, 4, 0], [26, 5, 1], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 2], [27, 1, 0], [27, 2, 0], [27, 3, 1], [27, 4, 0], [27, 5, 0], [27, 6, 1], [27, 7, 0], [27, 8, 0], [27, 9, 1], [27, 10, 0], [28, 0, 2], [28, 1, 0], [28, 2, 1], [28, 3, 0], [28, 4, 0], [28, 5, 0], [28, 6, 1], [28, 7, 0], [28, 8, 1], [28, 9, 0], [28, 10, 0], [29, 0, 2], [29, 1, 0], [29, 2, 1], [29, 3, 1], [29, 4, 0], [29, 5, 0], [29, 6, 1], [29, 7, 0], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 2], [30, 1, 0], [30, 2, 1], [30, 3, 1], [30, 4, 0], [30, 5, 1], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]], "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"], "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"], "max_value": 2}, "C": {"data": [[0, 0, 1], [0, 1, 0], [0, 2, 1], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 1], [0, 7, 0], [0, 8, 0], [0, 9, 1], [0, 10, 0], [1, 0, 1], [1, 1, 0], [1, 2, 0], [1, 3, 1], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 1], [3, 1, 0], [3, 2, 0], [3, 3, 1], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 1], [3, 8, 0], [3, 9, 1], [3, 10, 0], [4, 0, 1], [4, 1, 0], [4, 2, 0], [4, 3, 1], [4, 4, 0], [4, 5, 0], [4, 6, 0], [4, 7, 1], [4, 8, 0], [4, 9, 1], [4, 10, 0], [5, 0, 1], [5, 1, 0], [5, 2, 0], [5, 3, 1], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 1], [5, 8, 0], [5, 9, 0], [5, 10, 1], [6, 0, 1], [6, 1, 0], [6, 2, 1], [6, 3, 0], [6, 4, 0], [6, 5, 1], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 0], [7, 0, 1], [7, 1, 0], [7, 2, 1], [7, 3, 0], [7, 4, 0], [7, 5, 1], [7, 6, 0], [7, 7, 0], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 0], [8, 1, 0], [8, 2, 0], [8, 3, 0], [8, 4, 0], [8, 5, 0], [8, 6, 0], [8, 7, 0], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 0], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 1], [10, 1, 0], [10, 2, 1], [10, 3, 0], [10, 4, 0], [10, 5, 1], [10, 6, 0], [10, 7, 1], [10, 8, 0], [10, 9, 1], [10, 10, 0], [11, 0, 1], [11, 1, 0], [11, 2, 1], [11, 3, 0], [11, 4, 0], [11, 5, 1], [11, 6, 0], [11, 7, 1], [11, 8, 0], [11, 9, 1], [11, 10, 0], [12, 0, 1], [12, 1, 0], [12, 2, 1], [12, 3, 0], [12, 4, 0], [12, 5, 0], [12, 6, 0], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 1], [13, 1, 0], [13, 2, 1], [13, 3, 0], [13, 4, 0], [13, 5, 0], [13, 6, 0], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 1], [14, 1, 0], [14, 2, 0], [14, 3, 1], [14, 4, 0], [14, 5, 0], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 1], [15, 1, 0], [15, 2, 0], [15, 3, 1], [15, 4, 0], [15, 5, 0], [15, 6, 0], [15, 7, 0], [15, 8, 0], [15, 9, 0], [15, 10, 0], [16, 0, 0], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 1], [17, 1, 0], [17, 2, 0], [17, 3, 1], [17, 4, 0], [17, 5, 0], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 1], [18, 1, 0], [18, 2, 0], [18, 3, 1], [18, 4, 0], [18, 5, 0], [18, 6, 0], [18, 7, 1], [18, 8, 0], [18, 9, 1], [18, 10, 0], [19, 0, 1], [19, 1, 0], [19, 2, 0], [19, 3, 1], [19, 4, 0], [19, 5, 0], [19, 6, 0], [19, 7, 1], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 1], [20, 1, 0], [20, 2, 1], [20, 3, 0], [20, 4, 0], [20, 5, 0], [20, 6, 0], [20, 7, 0], [20, 8, 0], [20, 9, 0], [20, 10, 0], [21, 0, 1], [21, 1, 0], [21, 2, 0], [21, 3, 1], [21, 4, 0], [21, 5, 0], [21, 6, 1], [21, 7, 0], [21, 8, 0], [21, 9, 0], [21, 10, 0], [22, 0, 1], [22, 1, 0], [22, 2, 0], [22, 3, 1], [22, 4, 0], [22, 5, 0], [22, 6, 1], [22, 7, 0], [22, 8, 0], [22, 9, 0], [22, 10, 0], [23, 0, 0], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 1], [24, 1, 0], [24, 2, 0], [24, 3, 1], [24, 4, 0], [24, 5, 0], [24, 6, 0], [24, 7, 1], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 1], [25, 1, 0], [25, 2, 0], [25, 3, 1], [25, 4, 0], [25, 5, 0], [25, 6, 0], [25, 7, 1], [25, 8, 0], [25, 9, 1], [25, 10, 0], [26, 0, 1], [26, 1, 0], [26, 2, 1], [26, 3, 0], [26, 4, 0], [26, 5, 1], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 1], [27, 1, 0], [27, 2, 1], [27, 3, 0], [27, 4, 0], [27, 5, 0], [27, 6, 0], [27, 7, 0], [27, 8, 0], [27, 9, 0], [27, 10, 0], [28, 0, 1], [28, 1, 0], [28, 2, 0], [28, 3, 1], [28, 4, 0], [28, 5, 0], [28, 6, 0], [28, 7, 1], [28, 8, 0], [28, 9, 0], [28, 10, 0], [29, 0, 1], [29, 1, 0], [29, 2, 0], [29, 3, 1], [29, 4, 0], [29, 5, 0], [29, 6, 0], [29, 7, 1], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 0], [30, 1, 0], [30, 2, 0], [30, 3, 0], [30, 4, 0], [30, 5, 0], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]], "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"], "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"], "max_value": 1}, "\u6377\u901a": {"data": [[0, 0, 0], [0, 1, 0], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 0], [0, 7, 0], [0, 8, 0], [0, 9, 0], [0, 10, 0], [1, 0, 0], [1, 1, 0], [1, 2, 0], [1, 3, 0], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 0], [3, 1, 0], [3, 2, 0], [3, 3, 0], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 0], [3, 8, 0], [3, 9, 0], [3, 10, 0], [4, 0, 0], [4, 1, 0], [4, 2, 0], [4, 3, 0], [4, 4, 0], [4, 5, 0], [4, 6, 0], [4, 7, 0], [4, 8, 0], [4, 9, 0], [4, 10, 0], [5, 0, 0], [5, 1, 0], [5, 2, 0], [5, 3, 0], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 0], [5, 8, 0], [5, 9, 0], [5, 10, 0], [6, 0, 0], [6, 1, 0], [6, 2, 0], [6, 3, 0], [6, 4, 0], [6, 5, 0], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 0], [7, 0, 0], [7, 1, 0], [7, 2, 0], [7, 3, 0], [7, 4, 0], [7, 5, 0], [7, 6, 0], [7, 7, 0], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 0], [8, 1, 0], [8, 2, 0], [8, 3, 0], [8, 4, 0], [8, 5, 0], [8, 6, 0], [8, 7, 0], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 0], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 0], [10, 1, 0], [10, 2, 0], [10, 3, 0], [10, 4, 0], [10, 5, 0], [10, 6, 0], [10, 7, 0], [10, 8, 0], [10, 9, 0], [10, 10, 0], [11, 0, 0], [11, 1, 0], [11, 2, 0], [11, 3, 0], [11, 4, 0], [11, 5, 0], [11, 6, 0], [11, 7, 0], [11, 8, 0], [11, 9, 0], [11, 10, 0], [12, 0, 0], [12, 1, 0], [12, 2, 0], [12, 3, 0], [12, 4, 0], [12, 5, 0], [12, 6, 0], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 0], [13, 1, 0], [13, 2, 0], [13, 3, 0], [13, 4, 0], [13, 5, 0], [13, 6, 0], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 0], [14, 1, 0], [14, 2, 0], [14, 3, 0], [14, 4, 0], [14, 5, 0], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 0], [15, 1, 0], [15, 2, 0], [15, 3, 0], [15, 4, 0], [15, 5, 0], [15, 6, 0], [15, 7, 0], [15, 8, 0], [15, 9, 0], [15, 10, 0], [16, 0, 0], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 0], [17, 1, 0], [17, 2, 0], [17, 3, 0], [17, 4, 0], [17, 5, 0], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 0], [18, 1, 0], [18, 2, 0], [18, 3, 0], [18, 4, 0], [18, 5, 0], [18, 6, 0], [18, 7, 0], [18, 8, 0], [18, 9, 0], [18, 10, 0], [19, 0, 0], [19, 1, 0], [19, 2, 0], [19, 3, 0], [19, 4, 0], [19, 5, 0], [19, 6, 0], [19, 7, 0], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 0], [20, 1, 0], [20, 2, 0], [20, 3, 0], [20, 4, 0], [20, 5, 0], [20, 6, 0], [20, 7, 0], [20, 8, 0], [20, 9, 0], [20, 10, 0], [21, 0, 0], [21, 1, 0], [21, 2, 0], [21, 3, 0], [21, 4, 0], [21, 5, 0], [21, 6, 0], [21, 7, 0], [21, 8, 0], [21, 9, 0], [21, 10, 0], [22, 0, 0], [22, 1, 0], [22, 2, 0], [22, 3, 0], [22, 4, 0], [22, 5, 0], [22, 6, 0], [22, 7, 0], [22, 8, 0], [22, 9, 0], [22, 10, 0], [23, 0, 0], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 0], [24, 1, 0], [24, 2, 0], [24, 3, 0], [24, 4, 0], [24, 5, 0], [24, 6, 0], [24, 7, 0], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 0], [25, 1, 0], [25, 2, 0], [25, 3, 0], [25, 4, 0], [25, 5, 0], [25, 6, 0], [25, 7, 0], [25, 8, 0], [25, 9, 0], [25, 10, 0], [26, 0, 0], [26, 1, 0], [26, 2, 0], [26, 3, 0], [26, 4, 0], [26, 5, 0], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 0], [27, 1, 0], [27, 2, 0], [27, 3, 0], [27, 4, 0], [27, 5, 0], [27, 6, 0], [27, 7, 0], [27, 8, 0], [27, 9, 0], [27, 10, 0], [28, 0, 0], [28, 1, 0], [28, 2, 0], [28, 3, 0], [28, 4, 0], [28, 5, 0], [28, 6, 0], [28, 7, 0], [28, 8, 0], [28, 9, 0], [28, 10, 0], [29, 0, 0], [29, 1, 0], [29, 2, 0], [29, 3, 0], [29, 4, 0], [29, 5, 0], [29, 6, 0], [29, 7, 0], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 0], [30, 1, 0], [30, 2, 0], [30, 3, 0], [30, 4, 0], [30, 5, 0], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]], "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"], "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"], "max_value": 0}};

            // 整体热力图
            const overallChart = echarts.init(document.getElementById('overallHeatmap'));
            const overallOption = {
                title: {
                    text: 'ABC栋汇总作业热力图',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    position: 'top',
                    formatter: function(params) {
                        const date = overallHeatmapData.dates[params.value[0]];
                        const hour = overallHeatmapData.hours[params.value[1]];
                        const value = params.value[2];
                        return date + '<br/>' + hour + ': ' + value + '个作业';
                    }
                },
                grid: {
                    height: '60%',
                    top: '10%'
                },
                xAxis: {
                    type: 'category',
                    data: overallHeatmapData.dates,
                    splitArea: {
                        show: true
                    }
                },
                yAxis: {
                    type: 'category',
                    data: overallHeatmapData.hours,
                    splitArea: {
                        show: true
                    }
                },
                visualMap: {
                    min: 0,
                    max: overallHeatmapData.max_value,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '5%',
                    inRange: {
                        color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
                    }
                },
                series: [{
                    name: '作业数量',
                    type: 'heatmap',
                    data: overallHeatmapData.data,
                    label: {
                        show: true
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            overallChart.setOption(overallOption);

            // 各栋别热力图
            const buildings = ['A', 'B', 'C'];
            buildings.forEach(building => {
                const chartId = 'buildingHeatmap' + building;
                const chart = echarts.init(document.getElementById(chartId));
                const data = buildingHeatmapsData[building];

                if (!data) return;

                const option = {
                    title: {
                        text: building + '栋',
                        left: 'center',
                        textStyle: {
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        position: 'top',
                        formatter: function(params) {
                            const date = data.dates[params.value[0]];
                            const hour = data.hours[params.value[1]];
                            const value = params.value[2];
                            return date + '<br/>' + hour + ': ' + value + '个作业';
                        }
                    },
                    grid: {
                        height: '70%',
                        top: '15%'
                    },
                    xAxis: {
                        type: 'category',
                        data: data.dates,
                        splitArea: {
                            show: true
                        },
                        axisLabel: {
                            rotate: 45,
                            fontSize: 10
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: data.hours,
                        splitArea: {
                            show: true
                        },
                        axisLabel: {
                            fontSize: 10
                        }
                    },
                    visualMap: {
                        min: 0,
                        max: data.max_value,
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '2%',
                        itemHeight: 10,
                        itemWidth: 10,
                        textStyle: {
                            fontSize: 10
                        },
                        inRange: {
                            color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
                        }
                    },
                    series: [{
                        name: '作业数量',
                        type: 'heatmap',
                        data: data.data,
                        label: {
                            show: true,
                            fontSize: 10
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };
                chart.setOption(option);
            });

            // 日历热力图 - 改进版本
            const calendarChart = echarts.init(document.getElementById('calendarChart'));

            // 处理日历数据，按月份分组
            const calendarData = [["2025-08-01", 11], ["2025-08-02", 6], ["2025-08-03", 2], ["2025-08-04", 13], ["2025-08-05", 16], ["2025-08-06", 12], ["2025-08-07", 12], ["2025-08-08", 11], ["2025-08-09", 7], ["2025-08-10", 1], ["2025-08-11", 14], ["2025-08-12", 17], ["2025-08-13", 7], ["2025-08-14", 9], ["2025-08-15", 5], ["2025-08-16", 7], ["2025-08-17", 1], ["2025-08-18", 10], ["2025-08-19", 16], ["2025-08-20", 8], ["2025-08-21", 10], ["2025-08-22", 13], ["2025-08-23", 11], ["2025-08-24", 1], ["2025-08-25", 10], ["2025-08-26", 16], ["2025-08-27", 8], ["2025-08-28", 10], ["2025-08-29", 8], ["2025-08-30", 8], ["2025-08-31", 5]];
            const monthlyData = {};

            calendarData.forEach(item => {
                const date = new Date(item[0]);
                const monthKey = date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0');
                if (!monthlyData[monthKey]) {
                    monthlyData[monthKey] = [];
                }
                monthlyData[monthKey].push(item);
            });

            // 获取数据范围
            const allValues = calendarData.map(item => item[1]);
            const maxValue = Math.max(...allValues);
            const minValue = Math.min(...allValues);

            // 获取月份信息用于标题
            const dateRange = overallHeatmapData.dates;
            const startDate = new Date(dateRange[0]);
            const endDate = new Date(dateRange[dateRange.length - 1]);
            const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            const startMonth = monthNames[startDate.getMonth()];
            const endMonth = monthNames[endDate.getMonth()];
            const year = startDate.getFullYear();

            const calendarOption = {
                title: {
                    text: year + '年' + startMonth + (startMonth !== endMonth ? '~' + endMonth : '') + '作业日历热力图',
                    left: 'center',
                    top: 15,
                    textStyle: {
                        fontSize: 20,
                        fontWeight: 'bold',
                        color: '#333'
                    }
                },
                tooltip: {
                    position: 'top',
                    formatter: function(params) {
                        const date = new Date(params.value[0]);
                        const dateStr = date.getFullYear() + '年' +
                                       String(date.getMonth() + 1).padStart(2, '0') + '月' +
                                       String(date.getDate()).padStart(2, '0') + '日';
                        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                        const weekday = weekdays[date.getDay()];
                        return '<div style="padding: 8px;">' +
                               '<div style="font-weight: bold; margin-bottom: 4px;">' + dateStr + ' ' + weekday + '</div>' +
                               '<div style="color: #666;">作业数量: <span style="color: #007AFF; font-weight: bold;">' + params.value[1] + '</span> 个</div>' +
                               '</div>';
                    },
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333',
                        fontSize: 12
                    }
                },
                visualMap: {
                    show: false,
                    min: minValue,
                    max: maxValue,
                    inRange: {
                        color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
                    }
                },
                calendar: {
                    top: 60,
                    left: 80,
                    right: 80,
                    bottom: 80,
                    cellSize: [35, 35],
                    range: ['2025-08-01', '2025-08-31'],
                    itemStyle: {
                        borderWidth: 2,
                        borderColor: '#fff',
                        borderType: 'solid',
                        borderRadius: 4
                    },
                    yearLabel: {
                        show: false
                    },
                    monthLabel: {
                        show: true,
                        position: 'start',
                        margin: 10,
                        nameMap: ['1月', '2月', '3月', '4月', '5月', '6月',
                                 '7月', '8月', '9月', '10月', '11月', '12月'],
                        textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold',
                            color: '#333'
                        }
                    },
                    dayLabel: {
                        show: true,
                        position: 'start',
                        margin: 10,
                        nameMap: ['日', '一', '二', '三', '四', '五', '六'],
                        textStyle: {
                            fontSize: 14,
                            color: '#666',
                            fontWeight: 'bold'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                },
                series: [{
                    type: 'heatmap',
                    coordinateSystem: 'calendar',
                    data: calendarData,
                    emphasis: {
                        itemStyle: {
                            borderColor: '#333',
                            borderWidth: 2
                        }
                    }
                }]
            };
            calendarChart.setOption(calendarOption);

            // 栋别分布饼图
            const buildingChart = echarts.init(document.getElementById('buildingChart'));
            const buildingOption = {
                title: {
                    text: '各栋别作业分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [{
                    name: '作业分布',
                    type: 'pie',
                    radius: '70%',
                    data: [{"name": "B", "value": 171}, {"name": "C", "value": 86}, {"name": "A", "value": 28}],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            buildingChart.setOption(buildingOption);

            // 时间段分布柱状图
            const hourlyChart = echarts.init(document.getElementById('hourlyChart'));
            const hourlyOption = {
                title: {
                    text: '作业时间段分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"]
                },
                yAxis: {
                    type: 'value',
                    name: '作业数量'
                },
                series: [{
                    name: '作业数量',
                    type: 'bar',
                    data: [96, 1, 49, 32, 0, 30, 20, 21, 7, 13, 3],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#83bff6'},
                            {offset: 0.5, color: '#188df0'},
                            {offset: 1, color: '#188df0'}
                        ])
                    }
                }]
            };
            hourlyChart.setOption(hourlyOption);

            // 超时栋别分布
            const overtimeChart = echarts.init(document.getElementById('overtimeChart'));
            const overtimeOption = {
                title: {
                    text: '超时作业栋别分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [{
                    name: '超时分布',
                    type: 'pie',
                    radius: '70%',
                    data: [{"name": "B", "value": 17}, {"name": "C", "value": 17}, {"name": "A", "value": 1}],
                    itemStyle: {
                        color: function(params) {
                            const colors = ['#ff6b6b', '#feca57', '#48dbfb', '#ff9ff3'];
                            return colors[params.dataIndex % colors.length];
                        }
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            overtimeChart.setOption(overtimeOption);

            // 响应式处理
            window.addEventListener('resize', function() {
                overallChart.resize();
                calendarChart.resize();
                buildingChart.resize();
                hourlyChart.resize();
                overtimeChart.resize();

                // 调整各栋别热力图
                buildings.forEach(building => {
                    const chartId = 'buildingHeatmap' + building;
                    const chart = echarts.getInstanceByDom(document.getElementById(chartId));
                    if (chart) {
                        chart.resize();
                    }
                });
            });
        });
    </script>
</body>
</html>