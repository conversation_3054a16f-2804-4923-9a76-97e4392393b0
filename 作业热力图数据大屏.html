<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作业热力图与超时分析数据大屏</title>
    
    <!-- 核心依赖 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        /* v2.0 企业级色彩规范 */
        :root {
            /* 主色调 */
            --primary-bg: #0a0a0a;
            --secondary-bg: #141414;
            --tertiary-bg: #1f1f1f;
            --card-bg: rgba(20,20,20,0.95);
            
            /* 强调色系 */
            --accent-color: #E31937;
            --accent-light: #ff4757;
            
            /* 功能色系 */
            --success-color: #00d4aa;
            --warning-color: #ff9500;
            --error-color: #ff3b30;
            
            /* 文字色系 */
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            
            /* 边框与分割 */
            --border-color: #2a2a2a;
            --border-accent: #3a3a3a;
            --glow-color: rgba(227,25,55,0.3);
            
            /* 渐变系统 */
            --gradient-primary: linear-gradient(135deg, var(--accent-color), var(--accent-light));
            --gradient-bg: linear-gradient(135deg, var(--secondary-bg), var(--tertiary-bg));
            --gradient-glow: radial-gradient(circle, var(--glow-color), transparent);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, var(--glow-color) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0,212,170,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,149,0,0.05) 0%, transparent 50%);
            z-index: -2;
        }

        /* 浮动网格动画 */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(var(--border-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--border-color) 1px, transparent 1px);
            background-size: 60px 60px;
            opacity: 0.1;
            animation: gridFloat 30s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes gridFloat {
            0%, 100% { transform: translate(0, 0); }
            50% { transform: translate(10px, 10px); }
        }

        /* 玻璃拟态卡片 */
        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid var(--border-accent);
            position: relative;
            overflow: hidden;
            transform: translateZ(0);
            will-change: transform, opacity;
            transition: all 0.4s cubic-bezier(0.4,0,0.2,1);
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        }

        .glass-card:hover {
            transform: translateY(-4px) translateZ(0);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3), 0 0 40px var(--glow-color);
        }

        /* 标题样式 */
        .main-title {
            font-size: 3rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        /* 统计卡片 */
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }

        .stat-number {
            font-family: 'JetBrains Mono', monospace;
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px var(--glow-color);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .chart-container.large {
            height: 400px;
        }

        .chart-container.medium {
            height: 280px;
        }

        .chart-container canvas {
            max-height: 100% !important;
            width: 100% !important;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-good { 
            background: rgba(0,212,170,0.1); 
            color: var(--success-color);
            border: 1px solid rgba(0,212,170,0.2);
        }

        .status-warning { 
            background: rgba(255,149,0,0.1); 
            color: var(--warning-color);
            border: 1px solid rgba(255,149,0,0.2);
        }

        .status-error { 
            background: rgba(255,59,48,0.1); 
            color: var(--error-color);
            border: 1px solid rgba(255,59,48,0.2);
        }

        /* Fade-up 滚动动画 */
        .fade-up {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: all 1s cubic-bezier(0.4,0,0.2,1);
        }

        .fade-up.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
            
            .chart-container {
                height: 250px;
            }
            
            .chart-container.large {
                height: 300px;
            }
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--tertiary-bg);
            color: var(--text-secondary);
            font-weight: 600;
        }

        .data-table tbody tr:hover {
            background: rgba(255,255,255,0.02);
        }

        /* 徽章样式 */
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .badge-danger {
            background: var(--error-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="min-h-screen p-4 lg:p-8">
        <!-- 主标题 -->
        <div class="fade-up">
            <h1 class="main-title">
                <i class="fas fa-chart-line mr-4"></i>
                作业热力图与超时分析数据大屏
            </h1>
            <div class="text-center text-sm text-gray-400 mb-8">
                <i class="fas fa-calendar-alt mr-2"></i>分析期间: 2025-08-01 至 2025-08-31 |
                <i class="fas fa-clock ml-4 mr-2"></i>生成时间: 2025-08-21 11:24:23
            </div>
        </div>

        <!-- Bento Grid 布局 -->
        <div class="bento-grid grid grid-cols-12 gap-6">
            <!-- 统计概览卡片 -->
            <div class="col-span-12 fade-up">
                <div class="glass-card p-6">
                    <h2 class="text-xl font-semibold mb-6 text-center">
                        <i class="fas fa-chart-bar mr-2 text-red-500"></i>统计概览
                    </h2>
                    <div class="grid grid-cols-2 lg:grid-cols-5 gap-4">
                        <div class="stat-card">
                            <div class="stat-number" id="totalJobs">285</div>
                            <div class="stat-label">总作业数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="analysisDays">31</div>
                            <div class="stat-label">分析天数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="buildingCount">3</div>
                            <div class="stat-label">涉及栋别</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="overtimeJobs">35</div>
                            <div class="stat-label">超时作业</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="overtimeRate">12.28%</div>
                            <div class="stat-label">超时率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要图表区域 -->
            <div class="col-span-12 lg:col-span-8 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-fire mr-2 text-orange-500"></i>整体作业热力图（ABC栋汇总）
                    </h3>
                    <div class="chart-container large" id="overallHeatmap"></div>
                </div>
            </div>

            <!-- 栋别分布饼图 -->
            <div class="col-span-12 lg:col-span-4 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-building mr-2 text-blue-500"></i>栋别作业分布
                    </h3>
                    <div class="chart-container" id="buildingChart"></div>
                </div>
            </div>

            <!-- 时间段分布 -->
            <div class="col-span-12 lg:col-span-6 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-clock mr-2 text-green-500"></i>时间段分布
                    </h3>
                    <div class="chart-container" id="hourlyChart"></div>
                </div>
            </div>

            <!-- 超时分析 -->
            <div class="col-span-12 lg:col-span-6 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-exclamation-triangle mr-2 text-red-500"></i>超时栋别分布
                    </h3>
                    <div class="chart-container" id="overtimeChart"></div>
                </div>
            </div>

            <!-- 各栋别热力图 -->
            <div class="col-span-12 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-6">
                        <i class="fas fa-building mr-2 text-purple-500"></i>各栋别作业热力图
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="text-center font-medium mb-3 text-blue-400">A栋作业热力图</h4>
                            <div class="chart-container medium" id="buildingHeatmapA"></div>
                        </div>
                        <div>
                            <h4 class="text-center font-medium mb-3 text-green-400">B栋作业热力图</h4>
                            <div class="chart-container medium" id="buildingHeatmapB"></div>
                        </div>
                        <div>
                            <h4 class="text-center font-medium mb-3 text-yellow-400">C栋作业热力图</h4>
                            <div class="chart-container medium" id="buildingHeatmapC"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作业日历热力图 -->
            <div class="col-span-12 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-calendar-alt mr-2 text-indigo-500"></i>作业日历热力图
                    </h3>
                    <div class="chart-container large" id="calendarChart"></div>
                    <div class="flex justify-center mt-4 space-x-4 text-xs">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-700 rounded mr-2"></div>
                            <span>无作业</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-300 rounded mr-2"></div>
                            <span>轻度(1-2个)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span>中度(3-5个)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-700 rounded mr-2"></div>
                            <span>重度(6-10个)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-900 rounded mr-2"></div>
                            <span>极重(11个以上)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 超时作业详情 -->
            <div class="col-span-12 fade-up">
                <div class="glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-list mr-2 text-red-500"></i>超时作业详情
                    </h3>
                    <div class="overflow-x-auto max-h-96 overflow-y-auto">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>栋别</th>
                                    <th>作业内容</th>
                                    <th>结束时间</th>
                                    <th>超时时长</th>
                                </tr>
                            </thead>
                            <tbody id="overtimeTableBody">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据配置
        const Dashboard = {
            // 原始数据
            config: {
                // 整体热力图数据
                overallHeatmapData: {
                    "data": [[0, 0, 3], [0, 1, 0], [0, 2, 2], [0, 3, 1], [0, 4, 0], [0, 5, 0], [0, 6, 2], [0, 7, 0], [0, 8, 1], [0, 9, 1], [0, 10, 1], [1, 0, 3], [1, 1, 0], [1, 2, 0], [1, 3, 2], [1, 4, 0], [1, 5, 0], [1, 6, 1], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 1], [2, 1, 0], [2, 2, 1], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 4], [3, 1, 0], [3, 2, 2], [3, 3, 1], [3, 4, 0], [3, 5, 2], [3, 6, 0], [3, 7, 1], [3, 8, 0], [3, 9, 1], [3, 10, 0], [4, 0, 4], [4, 1, 0], [4, 2, 2], [4, 3, 2], [4, 4, 0], [4, 5, 3], [4, 6, 0], [4, 7, 2], [4, 8, 1], [4, 9, 1], [4, 10, 0], [5, 0, 4], [5, 1, 0], [5, 2, 2], [5, 3, 1], [5, 4, 0], [5, 5, 2], [5, 6, 0], [5, 7, 2], [5, 8, 0], [5, 9, 0], [5, 10, 1], [6, 0, 4], [6, 1, 0], [6, 2, 3], [6, 3, 0], [6, 4, 0], [6, 5, 2], [6, 6, 1], [6, 7, 1], [6, 8, 1], [6, 9, 0], [6, 10, 0], [7, 0, 4], [7, 1, 0], [7, 2, 3], [7, 3, 0], [7, 4, 0], [7, 5, 3], [7, 6, 0], [7, 7, 1], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 2], [8, 1, 0], [8, 2, 1], [8, 3, 1], [8, 4, 0], [8, 5, 1], [8, 6, 1], [8, 7, 1], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 1], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 4], [10, 1, 0], [10, 2, 3], [10, 3, 0], [10, 4, 0], [10, 5, 2], [10, 6, 0], [10, 7, 2], [10, 8, 0], [10, 9, 2], [10, 10, 0], [11, 0, 4], [11, 1, 0], [11, 2, 3], [11, 3, 1], [11, 4, 0], [11, 5, 2], [11, 6, 2], [11, 7, 1], [11, 8, 2], [11, 9, 1], [11, 10, 1], [12, 0, 3], [12, 1, 0], [12, 2, 2], [12, 3, 1], [12, 4, 0], [12, 5, 0], [12, 6, 1], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 4], [13, 1, 0], [13, 2, 3], [13, 3, 1], [13, 4, 0], [13, 5, 0], [13, 6, 1], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 2], [14, 1, 0], [14, 2, 1], [14, 3, 1], [14, 4, 0], [14, 5, 1], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 3], [15, 1, 0], [15, 2, 0], [15, 3, 2], [15, 4, 0], [15, 5, 0], [15, 6, 1], [15, 7, 0], [15, 8, 0], [15, 9, 1], [15, 10, 0], [16, 0, 1], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 4], [17, 1, 0], [17, 2, 2], [17, 3, 1], [17, 4, 0], [17, 5, 2], [17, 6, 0], [17, 7, 1], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 4], [18, 1, 1], [18, 2, 2], [18, 3, 1], [18, 4, 0], [18, 5, 2], [18, 6, 0], [18, 7, 2], [18, 8, 0], [18, 9, 2], [18, 10, 0], [19, 0, 3], [19, 1, 0], [19, 2, 1], [19, 3, 2], [19, 4, 0], [19, 5, 0], [19, 6, 1], [19, 7, 1], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 4], [20, 1, 0], [20, 2, 2], [20, 3, 2], [20, 4, 0], [20, 5, 0], [20, 6, 1], [20, 7, 0], [20, 8, 1], [20, 9, 0], [20, 10, 0], [21, 0, 4], [21, 1, 0], [21, 2, 1], [21, 3, 2], [21, 4, 0], [21, 5, 1], [21, 6, 2], [21, 7, 1], [21, 8, 0], [21, 9, 1], [21, 10, 0], [22, 0, 3], [22, 1, 0], [22, 2, 1], [22, 3, 2], [22, 4, 0], [22, 5, 1], [22, 6, 2], [22, 7, 0], [22, 8, 0], [22, 9, 1], [22, 10, 0], [23, 0, 1], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 3], [24, 1, 0], [24, 2, 2], [24, 3, 1], [24, 4, 0], [24, 5, 2], [24, 6, 0], [24, 7, 2], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 4], [25, 1, 0], [25, 2, 3], [25, 3, 1], [25, 4, 0], [25, 5, 1], [25, 6, 1], [25, 7, 1], [25, 8, 0], [25, 9, 1], [25, 10, 0], [26, 0, 3], [26, 1, 0], [26, 2, 2], [26, 3, 1], [26, 4, 0], [26, 5, 2], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 4], [27, 1, 0], [27, 2, 2], [27, 3, 1], [27, 4, 0], [27, 5, 0], [27, 6, 1], [27, 7, 0], [27, 8, 0], [27, 9, 1], [27, 10, 0], [28, 0, 3], [28, 1, 0], [28, 2, 1], [28, 3, 1], [28, 4, 0], [28, 5, 0], [28, 6, 1], [28, 7, 1], [28, 8, 1], [28, 9, 0], [28, 10, 0], [29, 0, 3], [29, 1, 0], [29, 2, 1], [29, 3, 2], [29, 4, 0], [29, 5, 0], [29, 6, 1], [29, 7, 1], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 2], [30, 1, 0], [30, 2, 1], [30, 3, 1], [30, 4, 0], [30, 5, 1], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]],
                    "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"],
                    "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
                    "max_value": 4
                },

                // 各栋别热力图数据
                buildingHeatmapsData: {
                    "A": {
                        "data": [[0, 0, 0], [0, 1, 0], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 0], [0, 7, 0], [0, 8, 0], [0, 9, 0], [0, 10, 0], [1, 0, 0], [1, 1, 0], [1, 2, 0], [1, 3, 0], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 1], [3, 1, 0], [3, 2, 0], [3, 3, 0], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 0], [3, 8, 0], [3, 9, 0], [3, 10, 0], [4, 0, 1], [4, 1, 0], [4, 2, 1], [4, 3, 0], [4, 4, 0], [4, 5, 1], [4, 6, 0], [4, 7, 0], [4, 8, 1], [4, 9, 0], [4, 10, 0], [5, 0, 1], [5, 1, 0], [5, 2, 0], [5, 3, 0], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 0], [5, 8, 0], [5, 9, 0], [5, 10, 0], [6, 0, 1], [6, 1, 0], [6, 2, 0], [6, 3, 0], [6, 4, 0], [6, 5, 0], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 0], [7, 0, 1], [7, 1, 0], [7, 2, 0], [7, 3, 0], [7, 4, 0], [7, 5, 0], [7, 6, 0], [7, 7, 0], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 0], [8, 1, 0], [8, 2, 0], [8, 3, 0], [8, 4, 0], [8, 5, 0], [8, 6, 0], [8, 7, 0], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 0], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 1], [10, 1, 0], [10, 2, 0], [10, 3, 0], [10, 4, 0], [10, 5, 0], [10, 6, 0], [10, 7, 0], [10, 8, 0], [10, 9, 0], [10, 10, 0], [11, 0, 1], [11, 1, 0], [11, 2, 1], [11, 3, 0], [11, 4, 0], [11, 5, 0], [11, 6, 1], [11, 7, 0], [11, 8, 1], [11, 9, 0], [11, 10, 0], [12, 0, 0], [12, 1, 0], [12, 2, 0], [12, 3, 0], [12, 4, 0], [12, 5, 0], [12, 6, 0], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 1], [13, 1, 0], [13, 2, 1], [13, 3, 0], [13, 4, 0], [13, 5, 0], [13, 6, 0], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 0], [14, 1, 0], [14, 2, 0], [14, 3, 0], [14, 4, 0], [14, 5, 0], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 0], [15, 1, 0], [15, 2, 0], [15, 3, 0], [15, 4, 0], [15, 5, 0], [15, 6, 0], [15, 7, 0], [15, 8, 0], [15, 9, 0], [15, 10, 0], [16, 0, 1], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 1], [17, 1, 0], [17, 2, 0], [17, 3, 0], [17, 4, 0], [17, 5, 0], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 1], [18, 1, 1], [18, 2, 0], [18, 3, 0], [18, 4, 0], [18, 5, 0], [18, 6, 0], [18, 7, 0], [18, 8, 0], [18, 9, 0], [18, 10, 0], [19, 0, 0], [19, 1, 0], [19, 2, 0], [19, 3, 0], [19, 4, 0], [19, 5, 0], [19, 6, 0], [19, 7, 0], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 1], [20, 1, 0], [20, 2, 0], [20, 3, 1], [20, 4, 0], [20, 5, 0], [20, 6, 0], [20, 7, 0], [20, 8, 0], [20, 9, 0], [20, 10, 0], [21, 0, 1], [21, 1, 0], [21, 2, 0], [21, 3, 0], [21, 4, 0], [21, 5, 0], [21, 6, 0], [21, 7, 0], [21, 8, 0], [21, 9, 0], [21, 10, 0], [22, 0, 0], [22, 1, 0], [22, 2, 0], [22, 3, 0], [22, 4, 0], [22, 5, 0], [22, 6, 0], [22, 7, 0], [22, 8, 0], [22, 9, 0], [22, 10, 0], [23, 0, 1], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 0], [24, 1, 0], [24, 2, 0], [24, 3, 0], [24, 4, 0], [24, 5, 0], [24, 6, 0], [24, 7, 0], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 1], [25, 1, 0], [25, 2, 1], [25, 3, 0], [25, 4, 0], [25, 5, 0], [25, 6, 1], [25, 7, 0], [25, 8, 0], [25, 9, 0], [25, 10, 0], [26, 0, 0], [26, 1, 0], [26, 2, 0], [26, 3, 0], [26, 4, 0], [26, 5, 0], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 1], [27, 1, 0], [27, 2, 1], [27, 3, 0], [27, 4, 0], [27, 5, 0], [27, 6, 0], [27, 7, 0], [27, 8, 0], [27, 9, 0], [27, 10, 0], [28, 0, 0], [28, 1, 0], [28, 2, 0], [28, 3, 0], [28, 4, 0], [28, 5, 0], [28, 6, 0], [28, 7, 0], [28, 8, 0], [28, 9, 0], [28, 10, 0], [29, 0, 0], [29, 1, 0], [29, 2, 0], [29, 3, 0], [29, 4, 0], [29, 5, 0], [29, 6, 0], [29, 7, 0], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 0], [30, 1, 0], [30, 2, 0], [30, 3, 0], [30, 4, 0], [30, 5, 0], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]],
                        "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"],
                        "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
                        "max_value": 1
                    },
                    "B": {
                        "data": [[0, 0, 2], [0, 1, 0], [0, 2, 1], [0, 3, 1], [0, 4, 0], [0, 5, 0], [0, 6, 1], [0, 7, 0], [0, 8, 1], [0, 9, 0], [0, 10, 1], [1, 0, 2], [1, 1, 0], [1, 2, 0], [1, 3, 1], [1, 4, 0], [1, 5, 0], [1, 6, 1], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 1], [2, 1, 0], [2, 2, 1], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 2], [3, 1, 0], [3, 2, 2], [3, 3, 0], [3, 4, 0], [3, 5, 2], [3, 6, 0], [3, 7, 0], [3, 8, 0], [3, 9, 0], [3, 10, 0], [4, 0, 2], [4, 1, 0], [4, 2, 1], [4, 3, 1], [4, 4, 0], [4, 5, 2], [4, 6, 0], [4, 7, 1], [4, 8, 0], [4, 9, 0], [4, 10, 0], [5, 0, 2], [5, 1, 0], [5, 2, 2], [5, 3, 0], [5, 4, 0], [5, 5, 2], [5, 6, 0], [5, 7, 1], [5, 8, 0], [5, 9, 0], [5, 10, 0], [6, 0, 2], [6, 1, 0], [6, 2, 2], [6, 3, 0], [6, 4, 0], [6, 5, 1], [6, 6, 1], [6, 7, 1], [6, 8, 1], [6, 9, 0], [6, 10, 0], [7, 0, 2], [7, 1, 0], [7, 2, 2], [7, 3, 0], [7, 4, 0], [7, 5, 2], [7, 6, 0], [7, 7, 1], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 2], [8, 1, 0], [8, 2, 1], [8, 3, 1], [8, 4, 0], [8, 5, 1], [8, 6, 1], [8, 7, 1], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 1], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 2], [10, 1, 0], [10, 2, 2], [10, 3, 0], [10, 4, 0], [10, 5, 1], [10, 6, 0], [10, 7, 1], [10, 8, 0], [10, 9, 1], [10, 10, 0], [11, 0, 2], [11, 1, 0], [11, 2, 1], [11, 3, 1], [11, 4, 0], [11, 5, 1], [11, 6, 1], [11, 7, 0], [11, 8, 1], [11, 9, 0], [11, 10, 1], [12, 0, 2], [12, 1, 0], [12, 2, 1], [12, 3, 1], [12, 4, 0], [12, 5, 0], [12, 6, 1], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 2], [13, 1, 0], [13, 2, 1], [13, 3, 1], [13, 4, 0], [13, 5, 0], [13, 6, 1], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 1], [14, 1, 0], [14, 2, 1], [14, 3, 0], [14, 4, 0], [14, 5, 1], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 2], [15, 1, 0], [15, 2, 0], [15, 3, 1], [15, 4, 0], [15, 5, 0], [15, 6, 1], [15, 7, 0], [15, 8, 0], [15, 9, 1], [15, 10, 0], [16, 0, 0], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 2], [17, 1, 0], [17, 2, 2], [17, 3, 0], [17, 4, 0], [17, 5, 2], [17, 6, 0], [17, 7, 1], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 2], [18, 1, 0], [18, 2, 2], [18, 3, 0], [18, 4, 0], [18, 5, 2], [18, 6, 0], [18, 7, 1], [18, 8, 0], [18, 9, 1], [18, 10, 0], [19, 0, 2], [19, 1, 0], [19, 2, 1], [19, 3, 1], [19, 4, 0], [19, 5, 0], [19, 6, 1], [19, 7, 0], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 2], [20, 1, 0], [20, 2, 1], [20, 3, 1], [20, 4, 0], [20, 5, 0], [20, 6, 1], [20, 7, 0], [20, 8, 1], [20, 9, 0], [20, 10, 0], [21, 0, 2], [21, 1, 0], [21, 2, 1], [21, 3, 1], [21, 4, 0], [21, 5, 1], [21, 6, 1], [21, 7, 1], [21, 8, 0], [21, 9, 1], [21, 10, 0], [22, 0, 2], [22, 1, 0], [22, 2, 1], [22, 3, 1], [22, 4, 0], [22, 5, 1], [22, 6, 1], [22, 7, 0], [22, 8, 0], [22, 9, 1], [22, 10, 0], [23, 0, 0], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 2], [24, 1, 0], [24, 2, 2], [24, 3, 0], [24, 4, 0], [24, 5, 2], [24, 6, 0], [24, 7, 1], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 2], [25, 1, 0], [25, 2, 2], [25, 3, 0], [25, 4, 0], [25, 5, 1], [25, 6, 0], [25, 7, 0], [25, 8, 0], [25, 9, 0], [25, 10, 0], [26, 0, 2], [26, 1, 0], [26, 2, 1], [26, 3, 1], [26, 4, 0], [26, 5, 1], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 2], [27, 1, 0], [27, 2, 0], [27, 3, 1], [27, 4, 0], [27, 5, 0], [27, 6, 1], [27, 7, 0], [27, 8, 0], [27, 9, 1], [27, 10, 0], [28, 0, 2], [28, 1, 0], [28, 2, 1], [28, 3, 0], [28, 4, 0], [28, 5, 0], [28, 6, 1], [28, 7, 0], [28, 8, 1], [28, 9, 0], [28, 10, 0], [29, 0, 2], [29, 1, 0], [29, 2, 1], [29, 3, 1], [29, 4, 0], [29, 5, 0], [29, 6, 1], [29, 7, 0], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 2], [30, 1, 0], [30, 2, 1], [30, 3, 1], [30, 4, 0], [30, 5, 1], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]],
                        "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"],
                        "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
                        "max_value": 2
                    },
                    "C": {
                        "data": [[0, 0, 1], [0, 1, 0], [0, 2, 1], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 1], [0, 7, 0], [0, 8, 0], [0, 9, 1], [0, 10, 0], [1, 0, 1], [1, 1, 0], [1, 2, 0], [1, 3, 1], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 0], [2, 0, 0], [2, 1, 0], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 0], [3, 0, 1], [3, 1, 0], [3, 2, 0], [3, 3, 1], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 1], [3, 8, 0], [3, 9, 1], [3, 10, 0], [4, 0, 1], [4, 1, 0], [4, 2, 0], [4, 3, 1], [4, 4, 0], [4, 5, 0], [4, 6, 0], [4, 7, 1], [4, 8, 0], [4, 9, 1], [4, 10, 0], [5, 0, 1], [5, 1, 0], [5, 2, 0], [5, 3, 1], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 1], [5, 8, 0], [5, 9, 0], [5, 10, 1], [6, 0, 1], [6, 1, 0], [6, 2, 1], [6, 3, 0], [6, 4, 0], [6, 5, 1], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 0], [7, 0, 1], [7, 1, 0], [7, 2, 1], [7, 3, 0], [7, 4, 0], [7, 5, 1], [7, 6, 0], [7, 7, 0], [7, 8, 0], [7, 9, 0], [7, 10, 0], [8, 0, 0], [8, 1, 0], [8, 2, 0], [8, 3, 0], [8, 4, 0], [8, 5, 0], [8, 6, 0], [8, 7, 0], [8, 8, 0], [8, 9, 0], [8, 10, 0], [9, 0, 0], [9, 1, 0], [9, 2, 0], [9, 3, 0], [9, 4, 0], [9, 5, 0], [9, 6, 0], [9, 7, 0], [9, 8, 0], [9, 9, 0], [9, 10, 0], [10, 0, 1], [10, 1, 0], [10, 2, 1], [10, 3, 0], [10, 4, 0], [10, 5, 1], [10, 6, 0], [10, 7, 1], [10, 8, 0], [10, 9, 1], [10, 10, 0], [11, 0, 1], [11, 1, 0], [11, 2, 1], [11, 3, 0], [11, 4, 0], [11, 5, 1], [11, 6, 0], [11, 7, 1], [11, 8, 0], [11, 9, 1], [11, 10, 0], [12, 0, 1], [12, 1, 0], [12, 2, 1], [12, 3, 0], [12, 4, 0], [12, 5, 0], [12, 6, 0], [12, 7, 0], [12, 8, 0], [12, 9, 0], [12, 10, 0], [13, 0, 1], [13, 1, 0], [13, 2, 1], [13, 3, 0], [13, 4, 0], [13, 5, 0], [13, 6, 0], [13, 7, 0], [13, 8, 0], [13, 9, 0], [13, 10, 0], [14, 0, 1], [14, 1, 0], [14, 2, 0], [14, 3, 1], [14, 4, 0], [14, 5, 0], [14, 6, 0], [14, 7, 0], [14, 8, 0], [14, 9, 0], [14, 10, 0], [15, 0, 1], [15, 1, 0], [15, 2, 0], [15, 3, 1], [15, 4, 0], [15, 5, 0], [15, 6, 0], [15, 7, 0], [15, 8, 0], [15, 9, 0], [15, 10, 0], [16, 0, 0], [16, 1, 0], [16, 2, 0], [16, 3, 0], [16, 4, 0], [16, 5, 0], [16, 6, 0], [16, 7, 0], [16, 8, 0], [16, 9, 0], [16, 10, 0], [17, 0, 1], [17, 1, 0], [17, 2, 0], [17, 3, 1], [17, 4, 0], [17, 5, 0], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 9, 0], [17, 10, 0], [18, 0, 1], [18, 1, 0], [18, 2, 0], [18, 3, 1], [18, 4, 0], [18, 5, 0], [18, 6, 0], [18, 7, 1], [18, 8, 0], [18, 9, 1], [18, 10, 0], [19, 0, 1], [19, 1, 0], [19, 2, 0], [19, 3, 1], [19, 4, 0], [19, 5, 0], [19, 6, 0], [19, 7, 1], [19, 8, 0], [19, 9, 0], [19, 10, 0], [20, 0, 1], [20, 1, 0], [20, 2, 1], [20, 3, 0], [20, 4, 0], [20, 5, 0], [20, 6, 0], [20, 7, 0], [20, 8, 0], [20, 9, 0], [20, 10, 0], [21, 0, 1], [21, 1, 0], [21, 2, 0], [21, 3, 1], [21, 4, 0], [21, 5, 0], [21, 6, 1], [21, 7, 0], [21, 8, 0], [21, 9, 0], [21, 10, 0], [22, 0, 1], [22, 1, 0], [22, 2, 0], [22, 3, 1], [22, 4, 0], [22, 5, 0], [22, 6, 1], [22, 7, 0], [22, 8, 0], [22, 9, 0], [22, 10, 0], [23, 0, 0], [23, 1, 0], [23, 2, 0], [23, 3, 0], [23, 4, 0], [23, 5, 0], [23, 6, 0], [23, 7, 0], [23, 8, 0], [23, 9, 0], [23, 10, 0], [24, 0, 1], [24, 1, 0], [24, 2, 0], [24, 3, 1], [24, 4, 0], [24, 5, 0], [24, 6, 0], [24, 7, 1], [24, 8, 0], [24, 9, 0], [24, 10, 0], [25, 0, 1], [25, 1, 0], [25, 2, 0], [25, 3, 1], [25, 4, 0], [25, 5, 0], [25, 6, 0], [25, 7, 1], [25, 8, 0], [25, 9, 1], [25, 10, 0], [26, 0, 1], [26, 1, 0], [26, 2, 1], [26, 3, 0], [26, 4, 0], [26, 5, 1], [26, 6, 0], [26, 7, 0], [26, 8, 0], [26, 9, 0], [26, 10, 0], [27, 0, 1], [27, 1, 0], [27, 2, 1], [27, 3, 0], [27, 4, 0], [27, 5, 0], [27, 6, 0], [27, 7, 0], [27, 8, 0], [27, 9, 0], [27, 10, 0], [28, 0, 1], [28, 1, 0], [28, 2, 0], [28, 3, 1], [28, 4, 0], [28, 5, 0], [28, 6, 0], [28, 7, 1], [28, 8, 0], [28, 9, 0], [28, 10, 0], [29, 0, 1], [29, 1, 0], [29, 2, 0], [29, 3, 1], [29, 4, 0], [29, 5, 0], [29, 6, 0], [29, 7, 1], [29, 8, 0], [29, 9, 0], [29, 10, 0], [30, 0, 0], [30, 1, 0], [30, 2, 0], [30, 3, 0], [30, 4, 0], [30, 5, 0], [30, 6, 0], [30, 7, 0], [30, 8, 0], [30, 9, 0], [30, 10, 0]],
                        "dates": ["2025-08-01", "2025-08-02", "2025-08-03", "2025-08-04", "2025-08-05", "2025-08-06", "2025-08-07", "2025-08-08", "2025-08-09", "2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14", "2025-08-15", "2025-08-16", "2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31"],
                        "hours": ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
                        "max_value": 1
                    }
                },

                // 超时作业详情数据
                overtimeData: [
                    {date: "2025-08-01", building: "B栋", task: "JH027-SC待检品", endTime: "18:30", overtime: "1.0小时"},
                    {date: "2025-08-01", building: "C栋", task: "JT028周转箱", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-01", building: "B栋", task: "JT026-24电池", endTime: "21:00", overtime: "3.5小时"},
                    {date: "2025-08-04", building: "C栋", task: "US001", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-04", building: "C栋", task: "US001", endTime: "21:30", overtime: "4.0小时"},
                    {date: "2025-08-04", building: "C栋", task: "US001", endTime: "23:30", overtime: "6.0小时"},
                    {date: "2025-08-05", building: "A栋", task: "UF009完成品", endTime: "18:30", overtime: "1.0小时"},
                    {date: "2025-08-05", building: "C栋", task: "US001", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-05", building: "C栋", task: "US001", endTime: "21:30", overtime: "4.0小时"},
                    {date: "2025-08-06", building: "C栋", task: "JT028完成品", endTime: "18:30", overtime: "1.0小时"},
                    {date: "2025-08-06", building: "C栋", task: "US001", endTime: "20:30", overtime: "3.0小时"},
                    {date: "2025-08-07", building: "B栋", task: "JT026-24电池", endTime: "19:00", overtime: "1.5小时"},
                    {date: "2025-08-11", building: "B栋", task: "JH027-SD待检品", endTime: "19:00", overtime: "1.5小时"},
                    {date: "2025-08-11", building: "C栋", task: "US001", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-11", building: "C栋", task: "US001", endTime: "21:30", overtime: "4.0小时"},
                    {date: "2025-08-12", building: "B栋", task: "JH027-SD待检品", endTime: "18:30", overtime: "1.0小时"},
                    {date: "2025-08-12", building: "B栋", task: "JH027-SD待检品", endTime: "20:30", overtime: "3.0小时"},
                    {date: "2025-08-12", building: "C栋", task: "US001", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-16", building: "B栋", task: "JT026-24电池", endTime: "19:30", overtime: "2.0小时"},
                    {date: "2025-08-19", building: "C栋", task: "US001", endTime: "19:30", overtime: "2.0小时"}
                ]
            },

            // 初始化
            init() {
                this.setupAnimations();
                this.initCharts();
                this.populateOvertimeTable();
                this.startCounterAnimations();
            },

            // 动画系统
            animations: {
                // 计数器动画
                counters() {
                    const counters = document.querySelectorAll('.stat-number');
                    counters.forEach(counter => {
                        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
                        const isPercentage = counter.textContent.includes('%');
                        let current = 0;
                        const increment = target / 60; // 60帧动画

                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            counter.textContent = isPercentage ?
                                current.toFixed(2) + '%' :
                                Math.floor(current).toString();
                        }, 1000 / 60);
                    });
                },

                // 滚动动画
                fadeUp() {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                entry.target.classList.add('visible');
                            }
                        });
                    }, { threshold: 0.1 });

                    document.querySelectorAll('.fade-up').forEach(el => {
                        observer.observe(el);
                    });
                }
            },

            // 设置动画
            setupAnimations() {
                this.animations.fadeUp();
                setTimeout(() => {
                    this.animations.counters();
                }, 800);
            },

            // 启动计数器动画
            startCounterAnimations() {
                setTimeout(() => {
                    this.animations.counters();
                }, 800);
            },

            // 填充超时作业表格
            populateOvertimeTable() {
                const tbody = document.getElementById('overtimeTableBody');
                tbody.innerHTML = this.config.overtimeData.map(item => `
                    <tr>
                        <td>${item.date}</td>
                        <td><span class="badge badge-primary">${item.building}</span></td>
                        <td>${item.task}</td>
                        <td><span class="badge badge-danger">${item.endTime}</span></td>
                        <td>${item.overtime}</td>
                    </tr>
                `).join('');
            },

            // 图表系统
            charts: {
                // 初始化整体热力图
                initOverallHeatmap() {
                    const ctx = document.getElementById('overallHeatmap');
                    if (!ctx) return;

                    // 由于Chart.js不直接支持热力图，我们使用散点图模拟
                    const data = Dashboard.config.overallHeatmapData;
                    const chartData = data.data.map(item => ({
                        x: item[0],
                        y: item[1],
                        v: item[2]
                    }));

                    new Chart(ctx, {
                        type: 'scatter',
                        data: {
                            datasets: [{
                                label: '作业数量',
                                data: chartData,
                                backgroundColor: (ctx) => {
                                    const value = ctx.parsed.v || 0;
                                    const alpha = value / data.max_value;
                                    return `rgba(227, 25, 55, ${alpha})`;
                                },
                                pointRadius: 15,
                                pointHoverRadius: 18
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        title: () => '',
                                        label: (context) => {
                                            const date = data.dates[context.parsed.x];
                                            const hour = data.hours[context.parsed.y];
                                            const value = context.parsed.v || 0;
                                            return `${date} ${hour}: ${value}个作业`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    type: 'linear',
                                    position: 'bottom',
                                    min: 0,
                                    max: data.dates.length - 1,
                                    ticks: {
                                        callback: (value) => data.dates[value] || '',
                                        color: '#a0a0a0'
                                    },
                                    grid: { color: '#2a2a2a' }
                                },
                                y: {
                                    min: 0,
                                    max: data.hours.length - 1,
                                    ticks: {
                                        callback: (value) => data.hours[value] || '',
                                        color: '#a0a0a0'
                                    },
                                    grid: { color: '#2a2a2a' }
                                }
                            }
                        }
                    });
                },

                // 初始化栋别分布饼图
                initBuildingChart() {
                    const ctx = document.getElementById('buildingChart');
                    if (!ctx) return;

                    // 计算各栋别作业总数
                    const buildingData = Dashboard.config.buildingHeatmapsData;
                    const buildingTotals = {
                        'A栋': buildingData.A.data.reduce((sum, item) => sum + item[2], 0),
                        'B栋': buildingData.B.data.reduce((sum, item) => sum + item[2], 0),
                        'C栋': buildingData.C.data.reduce((sum, item) => sum + item[2], 0)
                    };

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: Object.keys(buildingTotals),
                            datasets: [{
                                data: Object.values(buildingTotals),
                                backgroundColor: [
                                    'rgba(59, 130, 246, 0.8)',
                                    'rgba(16, 185, 129, 0.8)',
                                    'rgba(245, 158, 11, 0.8)'
                                ],
                                borderColor: [
                                    'rgb(59, 130, 246)',
                                    'rgb(16, 185, 129)',
                                    'rgb(245, 158, 11)'
                                ],
                                borderWidth: 3,
                                hoverBorderWidth: 5
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            cutout: '60%',
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: { color: '#a0a0a0' }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: (context) => {
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                                            return `${context.label}: ${context.parsed}个 (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                // 初始化时间段分布图
                initHourlyChart() {
                    const ctx = document.getElementById('hourlyChart');
                    if (!ctx) return;

                    // 计算各时间段作业总数
                    const data = Dashboard.config.overallHeatmapData;
                    const hourlyTotals = data.hours.map((hour, index) => {
                        return data.data.filter(item => item[1] === index)
                                      .reduce((sum, item) => sum + item[2], 0);
                    });

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: data.hours,
                            datasets: [{
                                label: '作业数量',
                                data: hourlyTotals,
                                backgroundColor: 'rgba(227, 25, 55, 0.8)',
                                borderColor: 'rgb(227, 25, 55)',
                                borderWidth: 2,
                                borderRadius: 6,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: (context) => `${context.label}: ${context.parsed.y}个作业`
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: { color: '#a0a0a0' },
                                    grid: { color: '#2a2a2a' }
                                },
                                y: {
                                    ticks: { color: '#a0a0a0' },
                                    grid: { color: '#2a2a2a' }
                                }
                            }
                        }
                    });
                },

                // 初始化超时分析图
                initOvertimeChart() {
                    const ctx = document.getElementById('overtimeChart');
                    if (!ctx) return;

                    // 统计各栋别超时数量
                    const overtimeByBuilding = Dashboard.config.overtimeData.reduce((acc, item) => {
                        acc[item.building] = (acc[item.building] || 0) + 1;
                        return acc;
                    }, {});

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: Object.keys(overtimeByBuilding),
                            datasets: [{
                                label: '超时作业数',
                                data: Object.values(overtimeByBuilding),
                                backgroundColor: [
                                    'rgba(59, 130, 246, 0.8)',
                                    'rgba(16, 185, 129, 0.8)',
                                    'rgba(245, 158, 11, 0.8)'
                                ],
                                borderColor: [
                                    'rgb(59, 130, 246)',
                                    'rgb(16, 185, 129)',
                                    'rgb(245, 158, 11)'
                                ],
                                borderWidth: 2,
                                borderRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: (context) => `${context.label}: ${context.parsed.y}个超时作业`
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: { color: '#a0a0a0' },
                                    grid: { color: '#2a2a2a' }
                                },
                                y: {
                                    ticks: { color: '#a0a0a0' },
                                    grid: { color: '#2a2a2a' }
                                }
                            }
                        }
                    });
                },

                // 初始化各栋别热力图
                initBuildingHeatmaps() {
                    const buildings = ['A', 'B', 'C'];
                    buildings.forEach(building => {
                        const ctx = document.getElementById(`buildingHeatmap${building}`);
                        if (!ctx) return;

                        const data = Dashboard.config.buildingHeatmapsData[building];
                        const chartData = data.data.map(item => ({
                            x: item[0],
                            y: item[1],
                            v: item[2]
                        }));

                        new Chart(ctx, {
                            type: 'scatter',
                            data: {
                                datasets: [{
                                    label: '作业数量',
                                    data: chartData,
                                    backgroundColor: (ctx) => {
                                        const value = ctx.parsed.v || 0;
                                        const alpha = value / data.max_value;
                                        const colors = {
                                            'A': 'rgba(59, 130, 246, ',
                                            'B': 'rgba(16, 185, 129, ',
                                            'C': 'rgba(245, 158, 11, '
                                        };
                                        return colors[building] + alpha + ')';
                                    },
                                    pointRadius: 8,
                                    pointHoverRadius: 10
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: { display: false },
                                    tooltip: {
                                        callbacks: {
                                            title: () => '',
                                            label: (context) => {
                                                const date = data.dates[context.parsed.x];
                                                const hour = data.hours[context.parsed.y];
                                                const value = context.parsed.v || 0;
                                                return `${date} ${hour}: ${value}个作业`;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    x: {
                                        type: 'linear',
                                        min: 0,
                                        max: data.dates.length - 1,
                                        ticks: {
                                            callback: (value) => {
                                                const date = data.dates[value];
                                                return date ? date.substring(5) : '';
                                            },
                                            color: '#a0a0a0',
                                            maxTicksLimit: 5
                                        },
                                        grid: { color: '#2a2a2a' }
                                    },
                                    y: {
                                        min: 0,
                                        max: data.hours.length - 1,
                                        ticks: {
                                            callback: (value) => data.hours[value] || '',
                                            color: '#a0a0a0'
                                        },
                                        grid: { color: '#2a2a2a' }
                                    }
                                }
                            }
                        });
                    });
                },

                // 初始化日历热力图
                initCalendarChart() {
                    const ctx = document.getElementById('calendarChart');
                    if (!ctx) return;

                    // 计算每日作业总数
                    const data = Dashboard.config.overallHeatmapData;
                    const dailyTotals = data.dates.map((date, index) => {
                        const total = data.data.filter(item => item[0] === index)
                                             .reduce((sum, item) => sum + item[2], 0);
                        return { x: date, y: total };
                    });

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: data.dates.map(date => date.substring(5)),
                            datasets: [{
                                label: '每日作业数',
                                data: dailyTotals.map(item => item.y),
                                backgroundColor: (ctx) => {
                                    const value = ctx.parsed.y;
                                    if (value === 0) return '#374151';
                                    if (value <= 2) return '#c6e48b';
                                    if (value <= 5) return '#7bc96f';
                                    if (value <= 10) return '#239a3b';
                                    return '#196127';
                                },
                                borderWidth: 1,
                                borderColor: '#2a2a2a'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: (context) => {
                                            const date = data.dates[context.dataIndex];
                                            return `${date}: ${context.parsed.y}个作业`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: '#a0a0a0',
                                        maxRotation: 45
                                    },
                                    grid: { color: '#2a2a2a' }
                                },
                                y: {
                                    ticks: { color: '#a0a0a0' },
                                    grid: { color: '#2a2a2a' }
                                }
                            }
                        }
                    });
                }
            },

            // 初始化所有图表
            initCharts() {
                this.charts.initOverallHeatmap();
                this.charts.initBuildingChart();
                this.charts.initHourlyChart();
                this.charts.initOvertimeChart();
                this.charts.initBuildingHeatmaps();
                this.charts.initCalendarChart();
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            Dashboard.init();
        });

        // 键盘支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F11') {
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            }
        });

        // 响应式图表调整
        window.addEventListener('resize', () => {
            Chart.helpers.each(Chart.instances, (instance) => {
                instance.resize();
            });
        });
    </script>
</body>
</html>
